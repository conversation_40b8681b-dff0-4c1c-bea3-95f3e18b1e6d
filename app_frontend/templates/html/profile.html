{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Patient Profile - Medical Analysis Platform</title>

    <!-- Performance Optimizations -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>

    <!-- Medical Professional Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>

    <!-- Medical Professional CSS -->
    <style>
        /* Modern CSS Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Medical Professional Color Palette */
        :root {
            /* Primary Medical Colors */
            --medical-primary: #0066cc;
            --medical-primary-dark: #004499;
            --medical-primary-light: #3385d6;
            --medical-secondary: #2c5aa0;
            --medical-accent: #00a651;

            /* Status Colors */
            --status-success: #00a651;
            --status-warning: #ff8c00;
            --status-error: #dc3545;
            --status-info: #17a2b8;

            /* Neutral Colors */
            --medical-white: #ffffff;
            --medical-gray-50: #f8f9fa;
            --medical-gray-100: #f1f3f4;
            --medical-gray-200: #e8eaed;
            --medical-gray-300: #dadce0;
            --medical-gray-400: #9aa0a6;
            --medical-gray-500: #5f6368;
            --medical-gray-600: #3c4043;
            --medical-gray-700: #202124;
            --medical-gray-800: #1a1a1a;

            /* Text Colors */
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --text-muted: #9aa0a6;
            --text-inverse: #ffffff;

            /* Background Colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #f1f3f4;

            /* Border Colors */
            --border-light: #e8eaed;
            --border-medium: #dadce0;
            --border-dark: #9aa0a6;

            /* Shadows */
            --shadow-sm: 0 1px 3px rgba(60, 64, 67, 0.15);
            --shadow-md: 0 2px 6px rgba(60, 64, 67, 0.15);
            --shadow-lg: 0 4px 12px rgba(60, 64, 67, 0.15);
            --shadow-xl: 0 8px 24px rgba(60, 64, 67, 0.15);

            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 24px;

            /* Transitions */
            --transition-fast: all 0.15s ease-in-out;
            --transition-normal: all 0.25s ease-in-out;
            --transition-slow: all 0.35s ease-in-out;
        }

        /* Medical Professional Typography */
        body {
            font-family: 'Inter', 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-secondary);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 14px;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            letter-spacing: -0.01em;
        }

        .medical-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
            background: var(--bg-secondary);
        }

        /* Medical Card System */
        .medical-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .medical-card-header {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-dark) 100%);
            color: var(--text-inverse);
            padding: 20px 24px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .medical-card-body {
            padding: 24px;
        }

        /* Medical Profile Header */
        .medical-profile-header {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-light);
            overflow: hidden;
            position: relative;
        }

        .medical-profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-accent) 100%);
        }

        .medical-profile-content {
            padding: 32px;
        }

        /* Medical Profile Section */
        .medical-profile-section {
            display: flex;
            align-items: flex-start;
            gap: 32px;
            margin-bottom: 32px;
        }

        .medical-avatar-container {
            position: relative;
            flex-shrink: 0;
        }

        .medical-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid var(--medical-primary-light);
            box-shadow: var(--shadow-lg);
            object-fit: cover;
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-inverse);
            font-size: 36px;
            font-weight: 600;
            transition: var(--transition-normal);
            position: relative;
        }

        .medical-avatar::after {
            content: '';
            position: absolute;
            inset: -3px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--medical-primary), var(--medical-accent));
            z-index: -1;
        }

        .medical-avatar:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .medical-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .medical-avatar span {
            font-family: 'Inter', sans-serif;
            font-size: 36px;
            font-weight: 700;
            color: var(--text-inverse);
            letter-spacing: 2px;
            text-transform: uppercase;
            user-select: none;
        }

        .medical-status-indicator {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: var(--status-success);
            border: 3px solid var(--bg-primary);
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
        }

        .medical-patient-info {
            flex: 1;
            min-width: 0;
        }

        .medical-patient-name {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.2;
            font-family: 'Inter', sans-serif;
        }

        .medical-patient-id {
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
            font-family: 'Source Sans Pro', monospace;
            letter-spacing: 0.5px;
        }

        .medical-contact-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 16px;
        }

        .medical-info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .medical-info-item i {
            width: 16px;
            color: var(--medical-primary);
            font-size: 14px;
        }

        .medical-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, var(--medical-accent) 0%, #00b359 100%);
            color: var(--text-inverse);
            padding: 6px 12px;
            border-radius: var(--radius-md);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 12px;
        }

        .medical-badge i {
            font-size: 12px;
        }

        /* Medical Statistics Grid */
        .medical-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-top: 32px;
        }

        .medical-stat-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: 24px;
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .medical-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--medical-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .medical-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .medical-stat-card:hover::before {
            transform: scaleX(1);
        }

        .medical-stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 20px;
            color: var(--text-inverse);
            box-shadow: var(--shadow-sm);
        }

        .medical-stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1;
            font-family: 'Inter', sans-serif;
        }

        .medical-stat-label {
            color: var(--text-secondary);
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            line-height: 1.2;
        }

        .medical-stat-card.total .medical-stat-icon { background: var(--medical-primary); }
        .medical-stat-card.total .medical-stat-number { color: var(--medical-primary); }

        .medical-stat-card.success .medical-stat-icon { background: var(--status-success); }
        .medical-stat-card.success .medical-stat-number { color: var(--status-success); }

        .medical-stat-card.warning .medical-stat-icon { background: var(--status-warning); }
        .medical-stat-card.warning .medical-stat-number { color: var(--status-warning); }

        .medical-stat-card.error .medical-stat-icon { background: var(--status-error); }
        .medical-stat-card.error .medical-stat-number { color: var(--status-error); }

        /* Medical Content Grid */
        .medical-content-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
            margin-top: 32px;
        }

        .medical-main-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
            overflow: hidden;
        }

        .medical-sidebar {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
            padding: 24px;
            height: fit-content;
        }

        .medical-section-header {
            padding: 20px 24px;
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-dark) 100%);
            color: var(--text-inverse);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid var(--border-light);
        }

        .history-list {
            padding: 0;
            max-height: 600px;
            overflow-y: auto;
        }

        .history-item {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: var(--transition);
        }

        .history-item:hover {
            background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
            transform: translateX(4px);
            box-shadow: var(--shadow-lg);
            color: #1e293b !important;
            border-left: 4px solid var(--primary-color);
        }

        .history-item:hover::before {
            transform: scaleY(1);
        }

        .history-item:hover .history-title,
        .history-item:hover .history-filename,
        .history-item:hover .history-details,
        .history-item:hover .history-date {
            color: #1e293b !important;
        }

        .history-item:hover .history-details .detail-item {
            color: #475569 !important;
        }

        .history-item:hover .history-filename span {
            color: #1e293b !important;
        }

        .history-item:hover .history-status {
            background: rgba(255, 255, 255, 0.9) !important;
            color: inherit !important;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .history-date {
            color: var(--text-muted);
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .history-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .history-filename {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .history-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .history-details {
            display: flex;
            gap: 1rem;
            margin: 0.5rem 0;
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-processing {
            background: #fef3c7;
            color: #92400e;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Medical Quick Actions */
        .medical-quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            padding: 24px;
        }

        .medical-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 24px 20px;
            background: var(--bg-primary);
            border: 2px solid var(--border-light);
            border-radius: var(--radius-lg);
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            transition: var(--transition-normal);
            text-align: center;
            min-height: 120px;
            position: relative;
            overflow: hidden;
        }

        .medical-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--medical-primary);
            transform: scaleX(0);
            transition: var(--transition-normal);
        }

        .medical-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--medical-primary);
            color: var(--text-primary);
            text-decoration: none;
        }

        .medical-action-btn:hover::before {
            transform: scaleX(1);
        }

        .medical-action-btn i {
            font-size: 24px;
            color: var(--medical-primary);
            margin-bottom: 4px;
        }

        .medical-action-btn span {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.3;
        }

        .medical-action-btn.primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-dark) 100%);
            color: var(--text-inverse);
            border-color: var(--medical-primary);
        }

        .medical-action-btn.primary i {
            color: var(--text-inverse);
        }

        .medical-action-btn.primary:hover {
            background: linear-gradient(135deg, var(--medical-primary-dark) 0%, var(--medical-secondary) 100%);
            color: var(--text-inverse);
        }

        /* Activity Summary */
        .activity-summary {
            background: var(--background-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .activity-value {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--text-muted);
        }

        .empty-state h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .empty-state p {
            font-size: 0.875rem;
        }

        /* Medical Navigation */
        .medical-back-btn {
            position: fixed;
            top: 24px;
            left: 24px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(12px);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: 12px 16px;
            color: var(--medical-primary);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-family: 'Inter', sans-serif;
        }

        .medical-back-btn:hover {
            background: var(--bg-primary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
            color: var(--medical-primary);
            text-decoration: none;
        }

        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .loading i {
            margin-right: 0.75rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Medical Responsive Design */
        @media (max-width: 1024px) {
            .medical-container {
                padding: 20px;
            }

            .medical-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .medical-quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .medical-container {
                padding: 16px;
            }

            .medical-content-grid {
                grid-template-columns: 1fr;
            }

            .medical-profile-section {
                flex-direction: column;
                text-align: center;
                gap: 24px;
            }

            .medical-stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .medical-profile-content {
                padding: 24px;
            }

            .medical-patient-name {
                font-size: 24px;
            }

            .medical-back-btn {
                top: 16px;
                left: 16px;
                padding: 10px 14px;
                font-size: 13px;
            }

            .medical-quick-actions {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .medical-action-btn {
                flex-direction: row;
                justify-content: flex-start;
                min-height: auto;
                padding: 16px 20px;
            }

            .medical-action-btn i {
                font-size: 20px;
                margin-bottom: 0;
            }
        }

        @media (max-width: 480px) {
            .medical-container {
                padding: 12px;
            }

            .medical-profile-content {
                padding: 20px;
            }

            .medical-avatar {
                width: 100px;
                height: 100px;
                font-size: 30px;
            }

            .medical-patient-name {
                font-size: 22px;
            }

            .medical-stat-card {
                padding: 20px;
            }

            .medical-stat-number {
                font-size: 28px;
            }

            .medical-quick-actions {
                padding: 20px;
            }
        }

        /* Medical Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Medical Professional Theme - Always Light */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #ffffff !important;
                --bg-secondary: #f8f9fa !important;
                --bg-tertiary: #f1f3f4 !important;
                --text-primary: #202124 !important;
                --text-secondary: #5f6368 !important;
                --text-muted: #9aa0a6 !important;
                --border-light: #e8eaed !important;
                --border-medium: #dadce0 !important;
            }
        }

        /* Medical Loading States */
        .medical-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: var(--text-muted);
            font-size: 14px;
        }

        .medical-loading i {
            margin-right: 12px;
            animation: medicalSpin 1s linear infinite;
            color: var(--medical-primary);
        }

        @keyframes medicalSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Medical Empty State */
        .medical-empty-state {
            text-align: center;
            padding: 60px 24px;
            color: var(--text-muted);
        }

        .medical-empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--medical-primary);
            opacity: 0.5;
        }

        .medical-empty-state h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }

        .medical-empty-state p {
            font-size: 14px;
            line-height: 1.5;
        }

        /* Legacy CSS removed - using medical styling */

        /* Old action button styles removed - using medical styling */

        /* Old navigation styles removed - using medical styling */

        /* Responsive Styles */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                padding: 2rem;
            }

            /* Old responsive styles removed - using medical responsive design */
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .stat-card {
                padding: 1.25rem;
            }

            /* Old section content styles removed */
        }
    </style>

    <!-- Application Configuration -->
    <script>
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
    </script>
</head>
<body>
    <!-- Medical Navigation -->
    <button class="medical-back-btn" onclick="goBack()" aria-label="Go back to previous page">
        <i class="fas fa-arrow-left" aria-hidden="true"></i>
        <span>Back</span>
    </button>

    <div class="medical-container">
        <!-- Medical Profile Header -->
        <div class="medical-profile-header">
            <div class="medical-profile-content">
                <div class="medical-profile-section">
                    <div class="medical-avatar-container">
                        <div id="profile-avatar" class="medical-avatar" role="img" aria-label="Patient avatar">
                            <!-- Avatar content will be populated by JavaScript -->
                        </div>
                        <div class="medical-status-indicator" title="Active Patient"></div>
                    </div>

                    <div class="medical-patient-info">
                        <h1 id="profile-username" class="medical-patient-name">Loading Information...</h1>
                        <div id="profile-patient-id" class="medical-patient-id">ID: Loading...</div>

                        <div class="medical-contact-info">
                            <div id="profile-email" class="medical-info-item">
                                <i class="fas fa-envelope" aria-hidden="true"></i>
                                <span>Loading...</span>
                            </div>
                            <div id="profile-joined" class="medical-info-item">
                                <i class="fas fa-calendar-plus" aria-hidden="true"></i>
                                <span>Loading...</span>
                            </div>
                        </div>

                        <div id="profile-badge" class="medical-badge" style="display: none;">
                            <i class="fas fa-star-of-life" aria-hidden="true"></i>
                            <span>Verified Patient</span>
                        </div>
                    </div>
                </div>

                <!-- Medical Statistics Grid -->
                <div class="medical-stats-grid">
                    <div class="medical-stat-card total">
                        <div class="medical-stat-icon">
                            <i class="fas fa-chart-line" aria-hidden="true"></i>
                        </div>
                        <div id="total-analyses" class="medical-stat-number">0</div>
                        <div class="medical-stat-label">Total Assessments</div>
                    </div>
                    <div class="medical-stat-card success">
                        <div class="medical-stat-icon">
                            <i class="fas fa-check-circle" aria-hidden="true"></i>
                        </div>
                        <div id="successful-analyses" class="medical-stat-number">0</div>
                        <div class="medical-stat-label">Completed</div>
                    </div>
                    <div class="medical-stat-card warning">
                        <div class="medical-stat-icon">
                            <i class="fas fa-hourglass-half" aria-hidden="true"></i>
                        </div>
                        <div id="pending-analyses" class="medical-stat-number">0</div>
                        <div class="medical-stat-label">In Progress</div>
                    </div>
                    <div class="medical-stat-card error">
                        <div class="medical-stat-icon">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                        </div>
                        <div id="failed-analyses" class="medical-stat-number">0</div>
                        <div class="medical-stat-label">Requires Review</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medical Content Grid -->
        <div class="medical-content-grid">
            <!-- Medical Quick Actions Section -->
            <div class="medical-card">
                <div class="medical-section-header">
                    <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                    <span>Quick Actions</span>
                </div>
                <div class="medical-quick-actions">
                    <a href="/audio_upload/" class="medical-action-btn primary" aria-label="Start new cognitive assessment">
                        <i class="fas fa-microphone" aria-hidden="true"></i>
                        <span>New Cognitive Assessment</span>
                    </a>
                    <a href="/audio_upload/history/" class="medical-action-btn" aria-label="View patient assessment history">
                        <i class="fas fa-history" aria-hidden="true"></i>
                        <span>Assessment History</span>
                    </a>
                    <a href="/user/settings/" class="medical-action-btn" aria-label="Patient account settings">
                        <i class="fas fa-user-cog" aria-hidden="true"></i>
                        <span>Account Settings</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * Medical Profile Application
         * Professional medical interface for patient profile management
         */
        class MedicalProfileApp {
            constructor() {
                this.config = {
                    apiBaseUrl: window.API_BASE_URL,
                    localBaseUrl: window.LOCAL_BASE_URL
                };

                this.cache = new Map();
                this.isLoading = false;

                this.init();
            }

            async init() {
                console.log('🏥 Initializing Medical Profile Application...');
                console.log('📊 Configuration:', this.config);

                try {
                    // Load patient profile data
                    await this.loadPatientProfile();

                    console.log('✅ Medical profile loaded successfully');
                } catch (error) {
                    console.error('❌ Failed to initialize medical profile:', error);
                    this.showMedicalError('Failed to load patient profile data');
                }
            }

            // Authentication helpers (using JWT)
            getAuthToken() {
                return localStorage.getItem('access_token');
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp < Date.now() / 1000;
                } catch {
                    return true;
                }
            }

            async makeAuthenticatedRequest(url, options = {}) {
                const token = this.getAuthToken();

                if (!token || this.isTokenExpired(token)) {
                    window.location.href = '/login/';
                    return null;
                }

                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                    return null;
                }

                return response;
            }

            // Load patient profile data
            async loadPatientProfile() {
                try {
                    const url = `${this.config.apiBaseUrl}/api/user/profile/`;
                    console.log('🔍 Loading patient profile from:', url);

                    const response = await this.makeAuthenticatedRequest(url);

                    if (!response) {
                        console.log('⚠️ No response received from server');
                        return;
                    }

                    console.log('📡 Response status:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('📋 Patient profile data received:', data);
                        this.updateMedicalProfileDisplay(data);
                        this.cache.set('patientProfile', data);
                    } else {
                        const errorText = await response.text();
                        console.error('❌ Response error:', errorText);

                        // Use fallback data for development
                        console.warn('⚠️ Using fallback patient data for development');
                        const fallbackData = {
                            user: {
                                first_name: 'John',
                                last_name: 'Doe',
                                email: '<EMAIL>',
                                date_joined: new Date().toISOString(),
                                avatar: null
                            },
                            stats: {
                                total: 0,
                                successful: 0,
                                pending: 0
                            }
                        };
                        this.updateMedicalProfileDisplay(fallbackData);
                    }
                } catch (error) {
                    console.error('❌ Failed to load patient profile:', error);
                    this.showMedicalError('Failed to load patient profile information');
                }
            }

            // Update medical profile display
            updateMedicalProfileDisplay(data) {
                console.log('📋 Updating medical profile display with data:', data);

                // Handle backend API response structure
                const user = data.data || data.user || data;
                const stats = data.stats || null;

                // Update patient information
                const patientName = user.display_name || user.full_name ||
                                  `${user.first_name || ''} ${user.last_name || ''}`.trim() ||
                                  (user.email ? user.email.split('@')[0] : 'Patient');

                document.getElementById('profile-username').textContent = patientName;

                // Update patient ID (using user ID)
                const patientIdElement = document.getElementById('profile-patient-id');
                if (patientIdElement) {
                    const patientId = user.id ? `${user.id.toString().slice(-6).toUpperCase()}` : 'UNKNOWN';
                    patientIdElement.textContent = `ID: ${patientId}`;
                }

                // Update contact information
                const emailElement = document.getElementById('profile-email');
                const emailSpan = emailElement.querySelector('span');
                if (emailSpan) {
                    emailSpan.textContent = user.email || 'No email on file';
                } else {
                    emailElement.innerHTML = `<i class="fas fa-envelope" aria-hidden="true"></i><span>${user.email || 'No email on file'}</span>`;
                }

                // Format registration date as "Patient since"
                if (user.date_joined) {
                    const joinDate = new Date(user.date_joined).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                    const joinedElement = document.getElementById('profile-joined');
                    const joinedSpan = joinedElement.querySelector('span');
                    if (joinedSpan) {
                        joinedSpan.textContent = `Joined since ${joinDate}`;
                    } else {
                        joinedElement.innerHTML = `<i class="fas fa-calendar-plus" aria-hidden="true"></i><span>Patient since ${joinDate}</span>`;
                    }
                }

                // Update medical avatar
                this.updateMedicalAvatar(user);

                // Update medical statistics
                if (stats) {
                    this.updateMedicalStatistics(stats);
                } else {
                    // Load statistics separately if not included
                    this.loadPatientStatistics();
                }

                // Show verified patient badge if applicable
                if (user.is_verified || user.is_premium) {
                    const badge = document.getElementById('profile-badge');
                    if (badge) {
                        badge.style.display = 'inline-flex';
                    }
                }
            }

            // Update medical avatar
            updateMedicalAvatar(user) {
                const avatar = document.getElementById('profile-avatar');

                // Check for avatar field from backend (ImageField)
                if (user.avatar && user.avatar !== null) {
                    // If avatar is a full URL, use it directly; otherwise prepend API base URL
                    const avatarUrl = user.avatar.startsWith('http') ?
                                    user.avatar :
                                    `${this.config.apiBaseUrl}${user.avatar}`;
                    avatar.innerHTML = `<img src="${avatarUrl}" alt="Patient avatar" loading="lazy">`;
                } else {
                    // Use medical initials based on first_name and last_name
                    const initials = this.getMedicalInitials(user.first_name, user.last_name, user.email);

                    // If no initials available, show empty avatar with just the background
                    if (initials && initials.trim() !== '') {
                        avatar.innerHTML = `<span>${initials}</span>`;
                    } else {
                        // Empty avatar - just show the gradient background
                        avatar.innerHTML = '';
                        console.log('📷 Showing empty avatar with gradient background only');
                    }
                }
            }

            // Get medical initials for patient
            getMedicalInitials(firstName, lastName, email) {
                console.log('🔤 Generating initials for:', { firstName, lastName, email });

                // Priority 1: First name + Last name (both must be non-empty)
                if (firstName && lastName && lastName.trim() !== '') {
                    const initials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                    console.log('✅ Using first + last name initials:', initials);
                    return initials;
                }

                // Priority 2: First name only (use only first character)
                if (firstName && firstName.trim() !== '') {
                    const initials = firstName.charAt(0).toUpperCase();
                    console.log('✅ Using first name initial only:', initials);
                    return initials;
                }

                // Priority 3: Last name only (use only first character)
                if (lastName && lastName.trim() !== '') {
                    const initials = lastName.charAt(0).toUpperCase();
                    console.log('✅ Using last name initial only:', initials);
                    return initials;
                }

                // Priority 4: Email username (use only first character)
                if (email) {
                    const emailUsername = email.split('@')[0];
                    if (emailUsername.length >= 1) {
                        const initials = emailUsername.charAt(0).toUpperCase();
                        console.log('✅ Using email initial:', initials);
                        return initials;
                    }
                }

                // Default fallback - return empty string
                console.log('⚠️ No name data available, using empty initials');
                return '';
            }

            // Load patient statistics from assessment history
            async loadPatientStatistics() {
                try {
                    console.log('📊 Loading patient assessment statistics...');
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (!response) return;

                    if (response.ok) {
                        const data = await response.json();
                        const assessmentItems = data.data || data.results || [];

                        // Calculate medical statistics from assessment data
                        const stats = this.calculateMedicalStatistics(assessmentItems);
                        this.updateMedicalStatistics(stats);
                    } else {
                        console.error('❌ Failed to load patient statistics');
                        // Use default medical stats
                        this.updateMedicalStatistics({
                            total: 0,
                            completed: 0,
                            processing: 0,
                            failed: 0
                        });
                    }
                } catch (error) {
                    console.error('❌ Failed to load patient statistics:', error);
                    this.updateMedicalStatistics({
                        total: 0,
                        completed: 0,
                        processing: 0,
                        failed: 0
                    });
                }
            }

            // Calculate medical statistics from assessment items
            calculateMedicalStatistics(assessmentItems) {
                const total = assessmentItems.length;
                const completed = assessmentItems.filter(item => item.status === 'completed').length;
                const processing = assessmentItems.filter(item => item.status === 'processing').length;
                const failed = assessmentItems.filter(item => item.status === 'failed').length;

                console.log('📊 Calculated medical statistics:', { total, completed, processing, failed });

                return {
                    total,
                    completed,
                    processing,
                    failed
                };
            }

            // Update medical statistics display
            updateMedicalStatistics(stats) {
                console.log('📊 Updating medical statistics display:', stats);

                const elements = {
                    'total-analyses': stats.total || 0,
                    'successful-analyses': stats.completed || stats.successful || 0,
                    'pending-analyses': stats.processing || stats.pending || 0,
                    'failed-analyses': stats.failed || 0
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        this.animateMedicalNumber(element, value);
                    }
                });
            }

            // Animate medical number changes with professional easing
            animateMedicalNumber(element, targetValue) {
                const startValue = parseInt(element.textContent) || 0;
                const duration = 1200; // Slightly longer for medical precision
                const startTime = performance.now();

                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Use easeOutCubic for professional feel
                    const easeProgress = 1 - Math.pow(1 - progress, 3);
                    const currentValue = Math.round(startValue + (targetValue - startValue) * easeProgress);
                    element.textContent = currentValue;

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                };

                requestAnimationFrame(animate);
            }

            // Removed recent history functionality for cleaner medical interface

            // Load activity summary
            async loadActivitySummary() {
                try {
                    // For now, calculate from history since the endpoint might not exist
                    this.calculateActivityFromHistory();
                } catch (error) {
                    console.error('Failed to load activity summary:', error);
                    this.calculateActivityFromHistory();
                }
            }

            // Update activity summary display
            updateActivitySummary(data) {
                const elements = {
                    'week-analyses': data.week_count || 0,
                    'month-analyses': data.month_count || 0,
                    'avg-score': data.average_score ? data.average_score.toFixed(1) : 'N/A'
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });
            }

            // Calculate activity from cached history if API endpoint doesn't exist
            calculateActivityFromHistory() {
                const history = this.cache.get('recentHistory') || [];
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

                const weekCount = history.filter(item =>
                    new Date(item.upload_time) >= weekAgo
                ).length;

                const monthCount = history.filter(item =>
                    new Date(item.upload_time) >= monthAgo
                ).length;

                // Calculate average MMSE score from completed analyses only
                const completedAnalyses = history.filter(item => item.status === 'completed');
                const scores = completedAnalyses
                    .map(item => {
                        const score = this.parseMMSEScore(item.result);
                        return score !== 'N/A' ? parseFloat(score) : null;
                    })
                    .filter(score => score !== null && !isNaN(score));

                const avgScore = scores.length > 0
                    ? (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(1)
                    : 'N/A';

                this.updateActivitySummary({
                    week_count: weekCount,
                    month_count: monthCount,
                    average_score: avgScore === 'N/A' ? null : parseFloat(avgScore)
                });
            }

            // Utility methods
            formatDate(dateString) {
                if (!dateString) return 'Unknown';
                try {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch {
                    return 'Invalid date';
                }
            }

            getStatusClass(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'status-completed';
                    case 'processing': return 'status-processing';
                    case 'failed': return 'status-failed';
                    default: return 'status-processing';
                }
            }

            getStatusText(status) {
                switch (status?.toLowerCase()) {
                    case 'completed': return 'Completed';
                    case 'processing': return 'Processing';
                    case 'failed': return 'Failed';
                    default: return 'Unknown';
                }
            }

            parseMMSEScore(result) {
                try {
                    if (!result) return 'N/A';
                    const parsed = typeof result === 'string' ? JSON.parse(result) : result;
                    const score = parsed['Predicted mmse score'];
                    return typeof score === 'number' ? score.toFixed(1) : (parseFloat(score) || 0).toFixed(1);
                } catch {
                    return 'N/A';
                }
            }

            formatRelationship(relationship) {
                const relationshipMap = {
                    'my_self': 'Myself',
                    'my_father': 'My Father',
                    'my_mother': 'My Mother',
                    'my_father_in_law': 'My Father in law',
                    'my_mother_in_law': 'My Mother in law',
                    'my_grandfather': 'My Grandfather',
                    'my_grandmother': 'My Grandmother',
                    'great_grandfather': 'My Great-Grandfather',
                    'great_grandmother': 'My Great-Grandmother',
                    'my_friend': 'My Friend',
                    'others': 'Other'
                };
                return relationshipMap[relationship] || relationship || 'Not specified';
            }

            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // Navigation methods
            viewAnalysisDetail(analysisId) {
                try {
                    // Find the analysis item from cached data
                    const recentHistory = this.cache.get('recentHistory') || [];
                    const analysisItem = recentHistory.find(item => item.id == analysisId);

                    if (analysisItem) {
                        console.log('Found analysis item in cache:', analysisItem);
                        // Store complete analysis details for details page
                        sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));
                        window.location.href = '/audio_upload/details/';
                    } else {
                        console.log('Analysis item not found in cache, fetching all history...');
                        // If not found in recent history, fetch all history and try again
                        this.fetchAllHistoryAndViewDetail(analysisId);
                    }
                } catch (error) {
                    console.error('Failed to navigate to details:', error);
                    alert('Failed to load analysis details. Please try again.');
                }
            }

            // Fetch all history and find the specific analysis
            async fetchAllHistoryAndViewDetail(analysisId) {
                try {
                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                    );

                    if (response && response.ok) {
                        const data = await response.json();
                        const allHistory = data.data || data.results || [];

                        // Find the specific analysis item
                        const analysisItem = allHistory.find(item => item.id == analysisId);

                        if (analysisItem) {
                            console.log('Found analysis item in full history:', analysisItem);
                            // Store complete analysis details for details page
                            sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));
                            window.location.href = '/audio_upload/details/';
                        } else {
                            console.error('Analysis item not found in history');
                            alert('Analysis record not found. It may have been deleted.');
                        }
                    } else {
                        console.error('Failed to fetch history');
                        alert('Failed to load analysis history. Please try again.');
                    }
                } catch (error) {
                    console.error('Failed to fetch history:', error);
                    alert('Failed to load analysis details. Please try again.');
                }
            }

            // Medical error handling
            showMedicalError(message) {
                console.error('🏥 Medical Profile Error:', message);

                // Show error in the main profile area
                const profileContainer = document.querySelector('.medical-profile-content');
                if (profileContainer) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'medical-error-state';
                    errorDiv.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--status-error);">
                            <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <h3 style="margin-bottom: 8px; color: var(--text-primary);">Medical Profile Error</h3>
                            <p style="color: var(--text-secondary);">${this.escapeHtml(message)}</p>
                            <button onclick="location.reload()" style="margin-top: 16px; padding: 8px 16px; background: var(--medical-primary); color: white; border: none; border-radius: var(--radius-md); cursor: pointer;">
                                Retry Loading
                            </button>
                        </div>
                    `;
                    profileContainer.appendChild(errorDiv);
                }
            }

            showHistoryError() {
                const container = document.getElementById('recent-history');
                if (container) {
                    container.innerHTML = `
                        <div class="empty-state error">
                            <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                            <h3>Failed to Load History</h3>
                            <p>Unable to load recent analysis history.</p>
                        </div>
                    `;
                }
            }
        }

        // Medical navigation function
        function goBack() {
            console.log('🔙 Medical navigation: Going back');
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // Return to medical dashboard
                window.location.href = '/audio_upload/';
            }
        }

        // Medical profile utility functions
        function refreshMedicalProfile() {
            console.log('🔄 Refreshing medical profile...');
            if (window.medicalProfileApp) {
                window.medicalProfileApp.loadPatientProfile();
            }
        }

        // Initialize medical application
        let medicalProfileApp;
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('🏥 DOM loaded, initializing Medical Profile Application...');
                medicalProfileApp = new MedicalProfileApp();
                window.medicalProfileApp = medicalProfileApp; // For debugging
            } catch (error) {
                console.error('❌ Failed to initialize medical profile app:', error);
            }
        });
    </script>
</body>
</html>
