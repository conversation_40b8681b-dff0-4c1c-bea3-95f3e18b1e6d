/* ===== PURPLE THEME DESIGN SYSTEM ===== */
/* Matching the homepage purple gradient theme */

:root {
    /* Purple Theme Color Palette - Matching Homepage */
    --primary-purple: #667eea;
    --primary-purple-dark: #764ba2;
    --secondary-purple: #9f7aea;
    --accent-teal: #0d9488;
    --accent-green: #059669;
    
    /* Text Colors */
    --text-dark: #1f2937;
    --text-gray: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --bg-purple-light: rgba(102, 126, 234, 0.05);
    --bg-purple-medium: rgba(102, 126, 234, 0.1);
    
    /* Border Colors */
    --border-light: #e5e7eb;
    --border-purple: rgba(102, 126, 234, 0.2);
    --border-purple-medium: rgba(102, 126, 234, 0.4);
    
    /* Shadow Effects */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-purple: 0 10px 15px -3px rgba(102, 126, 234, 0.1), 0 4px 6px -2px rgba(102, 126, 234, 0.05);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Base Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
    min-height: 100vh;
    color: var(--text-dark);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container Styles */
.purple-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.purple-card {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.purple-card-header {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
    color: var(--text-white);
    padding: var(--spacing-xl);
    text-align: center;
    position: relative;
}

.purple-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.purple-card-header h1,
.purple-card-header h2,
.purple-card-header h3 {
    position: relative;
    z-index: 1;
    margin: 0;
    font-weight: 700;
}

.purple-card-body {
    padding: var(--spacing-xl);
}

/* Button Styles */
.purple-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-size: 1rem;
}

.purple-btn-primary {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
    color: var(--text-white);
    box-shadow: var(--shadow-purple);
}

.purple-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--text-white);
    text-decoration: none;
}

.purple-btn-secondary {
    background: var(--bg-white);
    color: var(--primary-purple);
    border: 2px solid var(--primary-purple);
}

.purple-btn-secondary:hover {
    background: var(--primary-purple);
    color: var(--text-white);
    transform: translateY(-2px);
}

.purple-btn-accent {
    background: linear-gradient(135deg, var(--accent-teal) 0%, var(--accent-green) 100%);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.purple-btn-accent:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

/* Form Styles */
.purple-form-group {
    margin-bottom: var(--spacing-lg);
}

.purple-form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: var(--text-dark);
}

.purple-form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-white);
}

.purple-form-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Navigation Styles */
.purple-nav {
    background: var(--bg-white);
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--border-light);
}

.purple-nav-item {
    color: var(--text-gray);
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.purple-nav-item:hover {
    background: var(--bg-purple-light);
    color: var(--primary-purple);
    text-decoration: none;
}

.purple-nav-item.active {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
    color: var(--text-white);
}

/* Modal Styles */
.purple-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.purple-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.purple-modal {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.purple-modal-overlay.show .purple-modal {
    transform: scale(1);
}

.purple-modal-header {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
    color: var(--text-white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.purple-modal-body {
    padding: var(--spacing-xl);
}

/* Status Colors */
.purple-status-success {
    background: var(--accent-green);
    color: var(--text-white);
}

.purple-status-warning {
    background: #f59e0b;
    color: var(--text-white);
}

.purple-status-error {
    background: #ef4444;
    color: var(--text-white);
}

.purple-status-info {
    background: var(--primary-purple);
    color: var(--text-white);
}

/* Utility Classes */
.purple-text-center { text-align: center; }
.purple-text-left { text-align: left; }
.purple-text-right { text-align: right; }

.purple-mb-sm { margin-bottom: var(--spacing-sm); }
.purple-mb-md { margin-bottom: var(--spacing-md); }
.purple-mb-lg { margin-bottom: var(--spacing-lg); }
.purple-mb-xl { margin-bottom: var(--spacing-xl); }

.purple-mt-sm { margin-top: var(--spacing-sm); }
.purple-mt-md { margin-top: var(--spacing-md); }
.purple-mt-lg { margin-top: var(--spacing-lg); }
.purple-mt-xl { margin-top: var(--spacing-xl); }

.purple-p-sm { padding: var(--spacing-sm); }
.purple-p-md { padding: var(--spacing-md); }
.purple-p-lg { padding: var(--spacing-lg); }
.purple-p-xl { padding: var(--spacing-xl); }

/* Responsive Design */
@media (max-width: 768px) {
    .purple-container {
        padding: var(--spacing-md);
    }
    
    .purple-card-header,
    .purple-card-body,
    .purple-modal-body {
        padding: var(--spacing-md);
    }
}
