#!/usr/bin/env python
"""
Test script to verify Resend API configuration
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
import requests

def test_resend_api_key():
    """Test if Resend API key is valid"""
    api_key = os.environ.get('RESEND_API_KEY')
    if not api_key:
        print("❌ RESEND_API_KEY not found in environment variables")
        return False
    
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-4:]}")
    
    # Test API key by making a simple request
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test with a simple domains request
        response = requests.get('https://api.resend.com/domains', headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ API key is valid")
            domains = response.json()
            print(f"📧 Available domains: {[d.get('name', 'N/A') for d in domains.get('data', [])]}")
            return True
        else:
            print(f"❌ API key validation failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing API key: {e}")
        return False

def test_email_sending():
    """Test sending an email using Django's send_mail"""
    try:
        print("\n" + "="*50)
        print("Testing email sending...")
        print("="*50)
        
        result = send_mail(
            subject='Test Email from HiSage',
            message='This is a test email to verify Resend integration.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            html_message='''
            <html>
            <body>
                <h2>🧠 HiSage Test Email</h2>
                <p>This is a test email to verify Resend integration.</p>
                <p>If you receive this email, the configuration is working correctly!</p>
                <hr>
                <p><small>HiSage Team</small></p>
            </body>
            </html>
            ''',
            fail_silently=False
        )
        
        if result:
            print("✅ Email sent successfully!")
            return True
        else:
            print("❌ Email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Error sending email: {e}")
        return False

def main():
    print("🧪 Testing Resend Configuration")
    print("="*50)
    
    # Check environment variables
    print(f"EMAIL_SERVICE: {os.environ.get('EMAIL_SERVICE')}")
    print(f"DEFAULT_FROM_EMAIL: {os.environ.get('DEFAULT_FROM_EMAIL')}")
    print(f"Django EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    
    # Test API key
    if not test_resend_api_key():
        print("\n❌ API key test failed. Please check your RESEND_API_KEY.")
        return
    
    # Test email sending
    if test_email_sending():
        print("\n✅ All tests passed! Resend is configured correctly.")
    else:
        print("\n❌ Email sending test failed.")

if __name__ == '__main__':
    main()
