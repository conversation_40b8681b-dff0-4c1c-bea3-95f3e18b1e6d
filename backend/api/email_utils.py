from django.conf import settings
from django.core.mail import send_mail
from django.utils import timezone
from datetime import timedelta
import secrets

# Import URL configuration from backend settings
API_BASE_URL = settings.API_BASE_URL
LOCAL_BASE_URL = settings.LOCAL_BASE_URL


def get_name_from_email(email):
    """Extract username from email"""
    return email.split('@')[0].replace('.', ' ').strip().capitalize()


def create_verification_token():
    """Create verification token"""
    return secrets.token_urlsafe(32)


def generate_verification_code():
    """Generate 6-digit verification code"""
    import random
    return str(random.randint(100000, 999999))


def create_password_reset_token():
    """Create password reset token"""
    return secrets.token_urlsafe(32)




def send_verification_email(email):
    """Send email verification email"""
    try:
        from .models import EmailVerificationToken, CustomUser
        import os

        # Delete previous verification tokens for this user
        EmailVerificationToken.objects.filter(user__email=email).delete()

        user = CustomUser.objects.get(email=email)
        token = create_verification_token()
        verification_code = generate_verification_code()

        # Debug information
        print(f"🔍 Email sending debug info:")
        print(f"   Email: {email}")
        print(f"   Generated verification code: {verification_code}")
        print(f"   Verification code type: {type(verification_code)}")

        # Create verification token record
        try:
            verification_token = EmailVerificationToken.objects.create(
                user=user,
                token=token,
                verification_code=verification_code,
                expires_at=timezone.now() + timedelta(hours=24)
            )
        except Exception as e:
            # If verification_code field doesn't exist, use old method
            print(f"⚠️  Database field doesn't exist, using compatibility mode: {e}")
            verification_token = EmailVerificationToken.objects.create(
                user=user,
                token=token,
                expires_at=timezone.now() + timedelta(hours=24)
            )
            # Manually set verification code (if field exists)
            try:
                verification_token.verification_code = verification_code
                verification_token.save()
            except:
                print("⚠️  Cannot save verification code to database, using token as verification code")

        # Verify saved verification code
        try:
            saved_code = getattr(verification_token, 'verification_code', None)
            print(f"   Verification code saved to database: {saved_code}")
            if not saved_code:
                # If unable to save verification code, use first 6 characters of token
                verification_code = token[:6].upper()
                print(f"   Using first 6 characters of token as verification code: {verification_code}")
        except:
            # If field doesn't exist, use first 6 characters of token
            verification_code = token[:6].upper()
            print(f"   Field doesn't exist, using first 6 characters of token: {verification_code}")

        name = get_name_from_email(email)

        # Force verify verification code value
        print(f"🔍 Verification before email template construction:")
        print(f"   name: {name}")
        print(f"   verification_code: {verification_code}")
        print(f"   verification_code type: {type(verification_code)}")

        # Email subject
        subject = "🧠 Your Account Activation Code"

        # HTML email content
        html_message = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Activate Your Account</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #e9ecef;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .header p {{
                    color: #6c757d;
                    margin: 5px 0 0 0;
                    font-size: 16px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .content h2 {{
                    color: #2c3e50;
                    margin-bottom: 20px;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                    transition: transform 0.2s;
                }}
                .button:hover {{
                    transform: translateY(-2px);
                }}
                .link-box {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    border-left: 4px solid #667eea;
                    margin: 20px 0;
                    word-break: break-all;
                    font-family: monospace;
                    font-size: 14px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
                .warning {{
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧠 HiSage</h1>
                    <p>AI-Powered Voice Analysis Platform</p>
                </div>
                <div class="content">
                    <h2>Welcome, {name}!</h2>
                    <p>Thank you for registering with the <strong>HiSage Voice Analysis</strong> platform!</p>
                    <p>To complete your registration and activate your account, please use the following verification code:</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 10px; letter-spacing: 8px; display: inline-block;">
                            {verification_code}
                        </div>
                    </div>

                    <p style="text-align: center; color: #666; font-size: 14px;">Please enter the above 6-digit verification code on the activation page</p>

                    <div class="warning">
                        <p><strong>⚠️ Important Reminder:</strong></p>
                        <ul>
                            <li>This verification code will expire in 24 hours</li>
                            <li>If you did not register for this account, please ignore this email</li>
                            <li>For your security, please do not share this verification code with others</li>
                        </ul>
                    </div>

                    <div style="background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p><strong>🔒 Your Privacy Matters:</strong></p>
                        <p>All your data is encrypted and securely stored. We follow strict medical privacy standards to protect your information.</p>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>HiSage Team</strong></p>
                    <p>Technical Support: <strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """.format(name=name, verification_code=verification_code)

        # Plain text email content
        text_message = f"""
        Welcome to HiSage, {name}!

        Thank you for registering with our AI-powered dementia screening platform.

        To complete your registration and activate your account, please use the following verification code:

        Verification Code: {verification_code}

        Please enter the above 6-digit verification code on the activation page.

        Important Reminder: This verification code will expire in 24 hours.

        If you did not register for this account, please ignore this email.

        Best regards,
        HiSage Team

        Technical Support: <EMAIL>
        """

        # Debug information before sending email
        print("=" * 80)
        print(f"📧 Sending verification email to: {email}")
        print(f"🔢 Verification code: {verification_code}")
        print(f"📝 Email subject: {subject}")
        print("📄 HTML email content preview:")
        print(html_message[:500] + "..." if len(html_message) > 500 else html_message)
        print("📄 Text email content preview:")
        print(text_message[:300] + "..." if len(text_message) > 300 else text_message)
        print("=" * 80)

        # Simple direct email sending
        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )

        print("=" * 80)
        print(f"✅ Verification email sent to {email}")
        print(f"🔢 Verification code: {verification_code}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"❌ Failed to send verification email to {email}: {e}")
        return False


def send_password_reset_email(email, token):
    """Send password reset email"""
    try:
        name = get_name_from_email(email)

        # Build reset link - pointing to frontend page
        reset_link = f"{LOCAL_BASE_URL}/password-reset-confirm/?token={token}"

        # Email subject
        subject = "🔐 Reset Your Password"

        # HTML email content
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Password</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #e9ecef;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                }}
                .link-box {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    border-left: 4px solid #ff6b6b;
                    margin: 20px 0;
                    word-break: break-all;
                    font-family: monospace;
                    font-size: 14px;
                }}
                .warning {{
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧠 HiSage</h1>
                </div>
                <div class="content">
                    <h2>Password Reset Request</h2>
                    <p>Hello, {name}!</p>
                    <p>We received a request to reset your account password.</p>
                    <p>If this was your request, please click the button below to reset your password:</p>

                    <div style="text-align: center;">
                        <a href="{reset_link}" class="button">🔐 Reset Password</a>
                    </div>

                    <p>Or copy and paste this link into your browser:</p>
                    <div class="link-box">
                        {reset_link}
                    </div>

                    <div class="warning">
                        <p><strong>⚠️ Security Reminder:</strong></p>
                        <ul>
                            <li>This reset link will expire in 1 hour</li>
                            <li>If you did not request a password reset, please ignore this email</li>
                            <li>For your security, please do not share this link with others</li>
                        </ul>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>HiSage Team</strong></p>
                    <p>Technical Support: <strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text email content
        text_message = f"""
        Password Reset Request

        Hello, {name}!

        We received a request to reset your account password.

        If this was your request, please click the link below to reset your password:
        {reset_link}

        Important Reminder: This reset link will expire in 1 hour.

        If you did not request a password reset, please ignore this email.

        Best regards,
        HiSage Team

        Technical Support: <EMAIL>
        """
        
        # Send email
        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )
        
        print(f"✅ Password reset email sent to {email}")
        return True

    except Exception as e:
        print(f"❌ Failed to send password reset email to {email}: {e}")
        return False


def send_welcome_email(email):
    """Send welcome email (after account activation)"""
    try:
        name = get_name_from_email(email)
        
        # Email subject
        subject = "🎉 Welcome to HiSage!"

        # HTML email content
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .container {{
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .content {{
                    margin: 30px 0;
                }}
                .button {{
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    color: #6c757d;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome, {name}!</h1>
                </div>
                <div class="content">
                    <p>Your account has been successfully activated!</p>
                    <p>You can now log in and start using our AI-powered voice analysis platform.</p>

                    <div style="text-align: center;">
                        <a href="{LOCAL_BASE_URL}/login" class="button">🚀 Get Started</a>
                    </div>
                </div>
                <div class="footer">
                    <p><strong>HiSage Team</strong></p>
                    <p>Technical Support: <strong><EMAIL></strong></p>
                </div>
            </div>
        </body>
        </html>
        """

        text_message = f"""
        Welcome to HiSage, {name}!

        Your account has been successfully activated.
        You can now log in and start using our platform.

        Visit: {LOCAL_BASE_URL}/login

        Best regards,
        HiSage Team
        """

        print("=" * 80)
        print(f"🎉 Sending welcome email to: {email}")
        print("=" * 80)

        send_mail(
            subject=subject,
            message=text_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[email],
            html_message=html_message,
            fail_silently=False
        )

        print("=" * 80)
        print(f"✅ Welcome email sent to {email}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"❌ Failed to send welcome email to {email}: {e}")
        return False
