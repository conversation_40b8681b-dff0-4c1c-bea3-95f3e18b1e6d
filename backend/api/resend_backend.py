"""
Custom email backend for Resend API
"""
import os
import json
import requests
from django.core.mail.backends.base import BaseEmailBackend
from django.core.mail.message import EmailMessage
from django.conf import settings


class ResendEmailBackend(BaseEmailBackend):
    """
    Custom email backend that uses Resend API instead of SMTP
    """
    
    def __init__(self, fail_silently=False, **kwargs):
        super().__init__(fail_silently=fail_silently, **kwargs)
        self.api_key = os.environ.get('RESEND_API_KEY')
        self.api_url = 'https://api.resend.com/emails'
        
        if not self.api_key:
            if not self.fail_silently:
                raise ValueError("RESEND_API_KEY environment variable is required")
            print("⚠️ Warning: RESEND_API_KEY not configured")
    
    def send_messages(self, email_messages):
        """
        Send multiple email messages
        """
        if not email_messages:
            return 0
        
        sent_count = 0
        for message in email_messages:
            if self._send_message(message):
                sent_count += 1
        
        return sent_count
    
    def _send_message(self, message):
        """
        Send a single email message using Resend API
        """
        if not self.api_key:
            print("❌ Cannot send email: RESEND_API_KEY not configured")
            return False
        
        try:
            # Prepare email data for Resend API
            email_data = {
                'from': message.from_email or settings.DEFAULT_FROM_EMAIL,
                'to': message.to,
                'subject': message.subject,
            }
            
            # Add CC and BCC if present
            if message.cc:
                email_data['cc'] = message.cc
            if message.bcc:
                email_data['bcc'] = message.bcc
            
            # Handle both HTML and text content
            if hasattr(message, 'alternatives') and message.alternatives:
                # Find HTML content in alternatives
                for content, content_type in message.alternatives:
                    if content_type == 'text/html':
                        email_data['html'] = content
                        break
                # Set text content as fallback
                if message.body:
                    email_data['text'] = message.body
            else:
                # Only text content
                email_data['text'] = message.body
            
            # Debug information
            print("=" * 80)
            print(f"📧 Sending email via Resend API")
            print(f"   From: {email_data['from']}")
            print(f"   To: {email_data['to']}")
            print(f"   Subject: {email_data['subject']}")
            print(f"   Has HTML: {'html' in email_data}")
            print(f"   Has Text: {'text' in email_data}")
            print("=" * 80)
            
            # Send request to Resend API
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                json=email_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Email sent successfully via Resend API")
                print(f"   Email ID: {result.get('id', 'N/A')}")
                return True
            else:
                error_msg = f"Resend API error: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('message', 'Unknown error')}"
                except:
                    error_msg += f" - {response.text}"
                
                print(f"❌ Failed to send email via Resend API: {error_msg}")
                
                if not self.fail_silently:
                    raise Exception(error_msg)
                return False
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error when sending email via Resend API: {str(e)}"
            print(f"❌ {error_msg}")
            
            if not self.fail_silently:
                raise Exception(error_msg)
            return False
            
        except Exception as e:
            error_msg = f"Unexpected error when sending email via Resend API: {str(e)}"
            print(f"❌ {error_msg}")
            
            if not self.fail_silently:
                raise Exception(error_msg)
            return False
