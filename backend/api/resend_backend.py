"""
Custom email backend for Resend API
"""
import os
import json
import requests
import time
from django.core.mail.backends.base import BaseEmailBackend
from django.core.mail.message import EmailMessage
from django.conf import settings


class ResendEmailBackend(BaseEmailBackend):
    """
    Custom email backend that uses Resend API instead of SMTP
    """
    
    def __init__(self, fail_silently=False, **kwargs):
        super().__init__(fail_silently=fail_silently, **kwargs)
        self.api_key = os.environ.get('RESEND_API_KEY')
        self.api_url = 'https://api.resend.com/emails'
        
        if not self.api_key:
            if not self.fail_silently:
                raise ValueError("RESEND_API_KEY environment variable is required")
            print("⚠️ Warning: RESEND_API_KEY not configured")
    
    def send_messages(self, email_messages):
        """
        Send multiple email messages
        """
        if not email_messages:
            return 0
        
        sent_count = 0
        for message in email_messages:
            if self._send_message(message):
                sent_count += 1
        
        return sent_count
    
    def _send_message(self, message):
        """
        Send a single email message using Resend API
        """

        try:
            # Quick network connectivity check
            try:
                import socket
                socket.create_connection(("api.resend.com", 443), timeout=5)
                print("🌐 Network connectivity to api.resend.com: OK")
            except (socket.timeout, socket.error) as e:
                print(f"⚠️ Network connectivity issue detected: {e}")
                print("🔄 Proceeding with extended timeout strategy...")

            # Prepare email data for Resend API
            from_email = message.from_email or settings.DEFAULT_FROM_EMAIL

            email_data = {
                'from': from_email,
                'to': message.to,
                'subject': message.subject,
            }
            
            # Add CC and BCC if present
            if message.cc:
                email_data['cc'] = message.cc
            if message.bcc:
                email_data['bcc'] = message.bcc
            
            # Handle both HTML and text content
            if hasattr(message, 'alternatives') and message.alternatives:
                # Find HTML content in alternatives
                for content, content_type in message.alternatives:
                    if content_type == 'text/html':
                        email_data['html'] = content
                        break
                # Set text content as fallback
                if message.body:
                    email_data['text'] = message.body
            else:
                # Only text content
                email_data['text'] = message.body
            
            # Debug information
            print("=" * 80)
            print(f"📧 Sending email via Resend API")
            print(f"   From: {email_data['from']}")
            print(f"   To: {email_data['to']}")
            print(f"   Subject: {email_data['subject']}")
            print(f"   Has HTML: {'html' in email_data}")
            print(f"   Has Text: {'text' in email_data}")
            print("=" * 80)
            
            # Robust API call with multiple timeout strategies
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            # Try different timeout strategies
            timeout_strategies = [10, 20, 45]  # Progressive timeout increase

            for i, timeout_val in enumerate(timeout_strategies):
                try:
                    print(f"🔄 Network attempt {i+1}/{len(timeout_strategies)} (timeout: {timeout_val}s)")

                    # Configure session for better connection handling
                    session = requests.Session()
                    session.headers.update(headers)

                    response = session.post(
                        self.api_url,
                        json=email_data,
                        timeout=timeout_val,
                        verify=True  # Ensure SSL verification
                    )

                    # If we get here, the request succeeded
                    break

                except requests.exceptions.Timeout:
                    if i < len(timeout_strategies) - 1:
                        print(f"⏰ Timeout after {timeout_val}s, trying with longer timeout...")
                        continue
                    else:
                        # All timeout strategies failed
                        raise requests.exceptions.Timeout("All network attempts failed due to timeout")

                except requests.exceptions.ConnectionError as e:
                    if i < len(timeout_strategies) - 1:
                        print(f"🌐 Connection error: {str(e)[:100]}... Retrying...")
                        time.sleep(2)  # Brief pause before retry
                        continue
                    else:
                        raise
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Email sent successfully via Resend API")
                print(f"   Email ID: {result.get('id', 'N/A')}")
                return True
            else:
                error_msg = f"Resend API error: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('error', error_data.get('message', 'Unknown error'))}"

                    # Handle specific error cases
                    if response.status_code == 403:
                        if 'domain is not verified' in error_data.get('error', ''):
                            error_msg += "\n💡 Solution: Verify your domain at https://resend.com/domains"
                        elif 'testing emails' in error_data.get('error', ''):
                            error_msg += f"\n💡 Solution: For testing, use '<EMAIL>' as from address or verify your domain"

                    # Print detailed error information
                    if 'name' in error_data:
                        error_msg += f" (Error type: {error_data['name']})"
                except:
                    error_msg += f" - {response.text}"

                print(f"❌ Failed to send email via Resend API: {error_msg}")

                if not self.fail_silently:
                    raise Exception(error_msg)
                return False
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error when sending email via Resend API: {str(e)}"
            print(f"❌ {error_msg}")

            # Try fallback to console output for development
            fallback_enabled = getattr(settings, 'EMAIL_FALLBACK_TO_CONSOLE', True)
            if fallback_enabled:
                print("🔄 Network failed, using console fallback for development")
                print("=" * 80)
                print("📧 EMAIL FALLBACK - CONSOLE OUTPUT")
                print(f"   From: {email_data['from']}")
                print(f"   To: {email_data['to']}")
                print(f"   Subject: {email_data['subject']}")
                if 'html' in email_data:
                    print("   HTML Content:")
                    print(email_data['html'][:500] + "..." if len(email_data['html']) > 500 else email_data['html'])
                if 'text' in email_data:
                    print("   Text Content:")
                    print(email_data['text'][:300] + "..." if len(email_data['text']) > 300 else email_data['text'])
                print("=" * 80)
                print("✅ Email displayed in console (network fallback)")
                return True

            if not self.fail_silently:
                raise Exception(error_msg)
            return False
            
        except Exception as e:
            error_msg = f"Unexpected error when sending email via Resend API: {str(e)}"
            print(f"❌ {error_msg}")
            
            if not self.fail_silently:
                raise Exception(error_msg)
            return False


