"""
Simple Resend email backend - single attempt only
"""
import os
import requests
from django.core.mail.backends.base import BaseEmailBackend
from django.conf import settings


class SimpleResendEmailBackend(BaseEmailBackend):
    """
    Simple Resend email backend - single attempt, detailed debugging
    """
    
    def __init__(self, fail_silently=False, **kwargs):
        super().__init__(fail_silently=fail_silently, **kwargs)
        self.api_key = os.environ.get('RESEND_API_KEY')
        self.api_url = 'https://api.resend.com/emails'
        
        print(f"🔧 Initializing SimpleResendEmailBackend")
        print(f"   API Key: {self.api_key[:10] if self.api_key else 'None'}...{self.api_key[-4:] if self.api_key else ''}")
        print(f"   API URL: {self.api_url}")
        print(f"   Fail Silently: {fail_silently}")
        
        if not self.api_key:
            print("❌ RESEND_API_KEY not configured")
    
    def send_messages(self, email_messages):
        """Send multiple email messages"""
        if not email_messages:
            return 0
        
        sent_count = 0
        for message in email_messages:
            if self._send_message(message):
                sent_count += 1
        
        return sent_count
    
    def _send_message(self, message):
        """Send a single email message - one attempt only"""
        print("\n" + "=" * 80)
        print("📧 SIMPLE RESEND EMAIL BACKEND - SINGLE ATTEMPT")
        print("=" * 80)
        
        if not self.api_key:
            print("❌ Cannot send email: RESEND_API_KEY not configured")
            return False
        
        try:
            # Prepare email data
            from_email = message.from_email or settings.DEFAULT_FROM_EMAIL
            
            email_data = {
                'from': from_email,
                'to': message.to,
                'subject': message.subject,
            }
            
            # Handle HTML and text content
            if hasattr(message, 'alternatives') and message.alternatives:
                for content, content_type in message.alternatives:
                    if content_type == 'text/html':
                        email_data['html'] = content
                        break
                if message.body:
                    email_data['text'] = message.body
            else:
                email_data['text'] = message.body
            
            # Debug output
            print(f"📝 Email Details:")
            print(f"   From: {email_data['from']}")
            print(f"   To: {email_data['to']}")
            print(f"   Subject: {email_data['subject']}")
            print(f"   Has HTML: {'html' in email_data}")
            print(f"   Has Text: {'text' in email_data}")
            print(f"   API Key: {self.api_key[:10]}...{self.api_key[-4:]}")
            
            # Prepare headers
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            print(f"\n🚀 Making single API request to: {self.api_url}")
            print(f"   Timeout: 30 seconds")
            print(f"   Headers: Authorization=Bearer {self.api_key[:10]}..., Content-Type=application/json")
            
            # Single API call
            response = requests.post(
                self.api_url,
                headers=headers,
                json=email_data,
                timeout=30
            )
            
            # Detailed response analysis
            print(f"\n📡 API Response:")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response Time: {response.elapsed.total_seconds():.2f} seconds")
            print(f"   Response Headers: {dict(response.headers)}")
            print(f"   Response Body: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                email_id = result.get('id', 'N/A')
                print(f"\n✅ SUCCESS: Email sent successfully!")
                print(f"   Email ID: {email_id}")
                print("=" * 80)
                return True
            else:
                print(f"\n❌ FAILED: API returned error status {response.status_code}")
                print(f"   Error Response: {response.text}")
                print("=" * 80)
                
                if not self.fail_silently:
                    raise Exception(f"Resend API error: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.Timeout as e:
            print(f"\n❌ TIMEOUT ERROR:")
            print(f"   Error: {str(e)}")
            print(f"   This means the request to {self.api_url} took longer than 30 seconds")
            print(f"   Possible causes: Network issues, DNS problems, firewall blocking")
            print("=" * 80)
            
            if not self.fail_silently:
                raise Exception(f"Network timeout: {str(e)}")
            return False
            
        except requests.exceptions.ConnectionError as e:
            print(f"\n❌ CONNECTION ERROR:")
            print(f"   Error: {str(e)}")
            print(f"   This means cannot connect to {self.api_url}")
            print(f"   Possible causes: DNS issues, network routing, firewall")
            print("=" * 80)
            
            if not self.fail_silently:
                raise Exception(f"Connection error: {str(e)}")
            return False
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ REQUEST ERROR:")
            print(f"   Error: {str(e)}")
            print(f"   Error Type: {type(e).__name__}")
            print("=" * 80)
            
            if not self.fail_silently:
                raise Exception(f"Request error: {str(e)}")
            return False
            
        except Exception as e:
            print(f"\n❌ UNEXPECTED ERROR:")
            print(f"   Error: {str(e)}")
            print(f"   Error Type: {type(e).__name__}")
            print("=" * 80)
            
            if not self.fail_silently:
                raise Exception(f"Unexpected error: {str(e)}")
            return False
