from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'api'

urlpatterns = [
    # 用户认证相关
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.UserLogoutView.as_view(), name='logout'),
    path('verify-code/', views.VerifyCodeView.as_view(), name='verify_code'),
    path('verify-email/', views.EmailVerificationView.as_view(), name='verify_email'),
    path('resend-verification/', views.ResendVerificationView.as_view(), name='resend_verification'),

    # User profile related
    path('user/profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('user/change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    path('password-reset/', views.PasswordResetRequestView.as_view(), name='password_reset'),
    path('password-reset-confirm/', views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('check-email/', views.CheckEmailView.as_view(), name='check_email'),

    # JWT Token related
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Audio analysis related
    path('audio_upload/', views.AudioUploadView.as_view(), name='audio_upload'),
    path('audio_history/', views.AudioHistoryView.as_view(), name='audio_history'),
    path('audio-analyses/<uuid:analysis_id>/', views.AudioAnalysisDetailView.as_view(), name='audio_analysis_detail'),

    # Donation related
    path('donations/create/', views.DonationCreateView.as_view(), name='donation_create'),
    path('donations/confirm/', views.DonationConfirmView.as_view(), name='donation_confirm'),
    path('donations/status/<uuid:analysis_id>/', views.DonationStatusView.as_view(), name='donation_status'),
    path('stripe/config/', views.StripeConfigView.as_view(), name='stripe_config'),

    # User agreement related
    path('user/terms-agreement/', views.UserTermsAgreementView.as_view(), name='user_terms_agreement'),

    # System related
    path('health/', views.HealthCheckView.as_view(), name='health_check'),
]