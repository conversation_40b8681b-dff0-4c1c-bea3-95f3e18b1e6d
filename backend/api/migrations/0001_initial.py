# Generated by Django 5.2.3 on 2025-07-29 04:17

import api.models
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="Email Address"
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=30, verbose_name="First Name"
                    ),
                ),
                (
                    "last_name",
                    models.Char<PERSON><PERSON>(
                        blank=True, max_length=30, verbose_name="Last Name"
                    ),
                ),
                (
                    "is_active",
                    models.<PERSON>oleanField(default=False, verbose_name="Is Active"),
                ),
                (
                    "is_staff",
                    models.Boolean<PERSON>ield(default=False, verbose_name="Is Staff"),
                ),
                (
                    "is_superuser",
                    models.BooleanField(default=False, verbose_name="Is Superuser"),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="Date Joined"
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Login"
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="avatars/",
                        verbose_name="Avatar",
                    ),
                ),
                (
                    "has_agreed_to_terms",
                    models.BooleanField(
                        default=False, verbose_name="Has Agreed to Terms"
                    ),
                ),
                (
                    "terms_agreed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Terms Agreed At"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "db_table": "api_customuser",
            },
        ),
        migrations.CreateModel(
            name="AudioAnalysis",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("filename", models.CharField(max_length=255, verbose_name="Filename")),
                (
                    "audio_file",
                    models.FileField(
                        upload_to=api.models.user_upload_path,
                        verbose_name="Audio File Path",
                    ),
                ),
                (
                    "result",
                    models.TextField(
                        blank=True, null=True, verbose_name="Analysis Result"
                    ),
                ),
                (
                    "upload_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="Upload Time"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="processing",
                        max_length=20,
                        verbose_name="Analysis Status",
                    ),
                ),
                (
                    "relationship",
                    models.CharField(
                        blank=True,
                        help_text="Relationship to speaker (e.g., Myself, My Father, My Mother)",
                        max_length=50,
                        null=True,
                        verbose_name="Audio Speaker",
                    ),
                ),
                (
                    "occupation",
                    models.CharField(
                        blank=True,
                        help_text="Speaker's occupation",
                        max_length=100,
                        null=True,
                        verbose_name="Occupation",
                    ),
                ),
                (
                    "age",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Speaker's age",
                        null=True,
                        verbose_name="Age",
                    ),
                ),
                (
                    "date_of_birth",
                    models.CharField(
                        blank=True,
                        help_text="Speaker's date of birth (DD-MM-YYYY format)",
                        max_length=20,
                        null=True,
                        verbose_name="Date of Birth",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audio_analyses",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Audio Analysis",
                "verbose_name_plural": "Audio Analyses",
                "ordering": ["-upload_time"],
            },
        ),
        migrations.CreateModel(
            name="Donation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "amount",
                    models.PositiveIntegerField(
                        default=150, verbose_name="Donation Amount (USD)"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="Donation Status",
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Payment Intent ID",
                    ),
                ),
                (
                    "stripe_charge_id",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Stripe Charge ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Completed At"
                    ),
                ),
                (
                    "audio_analysis",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="donations",
                        to="api.audioanalysis",
                        verbose_name="Audio Analysis",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="donations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Donation",
                "verbose_name_plural": "Donations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                (
                    "verification_code",
                    models.CharField(
                        blank=True,
                        max_length=6,
                        null=True,
                        verbose_name="Verification Code",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("token", models.CharField(max_length=255, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "密码重置令牌",
                "verbose_name_plural": "密码重置令牌",
            },
        ),
    ]
