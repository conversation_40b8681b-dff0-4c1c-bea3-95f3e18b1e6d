import os
import requests

# Load environment variables from .env file
def load_env():
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load environment
load_env()

# Test Resend API
api_key = os.environ.get('RESEND_API_KEY')
print(f"API Key: {api_key[:10] if api_key else 'None'}...")

if api_key:
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # First, check domains
    print("\n=== Checking Resend Domains ===")
    try:
        response = requests.get('https://api.resend.com/domains', headers=headers, timeout=10)
        print(f"Domains API Status Code: {response.status_code}")
        if response.status_code == 200:
            domains = response.json()
            print(f"Available domains: {[d.get('name', 'N/A') for d in domains.get('data', [])]}")
        else:
            print(f"Domains API Error: {response.text}")
    except Exception as e:
        print(f"Error checking domains: {e}")
    
    # Test email data with a verified domain
    print("\n=== Testing Email Sending ===")
    
    # Try with different from addresses
    from_addresses = [
        '<EMAIL>',  # Resend's default test domain
        '<EMAIL>',  # Your configured domain
    ]
    
    for from_email in from_addresses:
        print(f"\nTrying with from address: {from_email}")
        
        # For testing with unverified domain, we can only send to the account owner's email
        # For production, you can send to any email after domain verification
        test_recipient = '<EMAIL>' if from_email == '<EMAIL>' else '<EMAIL>'

        email_data = {
            'from': from_email,
            'to': [test_recipient],
            'subject': 'Test Email from HiSage',
            'html': '''
            <html>
            <body>
                <h2>🧠 HiSage Test Email</h2>
                <p>This is a test email to verify Resend integration.</p>
                <p>If you receive this email, the configuration is working correctly!</p>
                <hr>
                <p><small>HiSage Team</small></p>
            </body>
            </html>
            ''',
            'text': 'This is a test email to verify Resend integration.'
        }
        
        try:
            response = requests.post(
                'https://api.resend.com/emails',
                headers=headers,
                json=email_data,
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                print("✅ Email sent successfully!")
                break
            else:
                print("❌ Email sending failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
else:
    print("❌ No API key found")
