#!/usr/bin/env python
"""
Test network connectivity to Resend API
"""
import os
import requests
import time
import socket

# Load environment variables from .env file
def load_env():
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def test_dns_resolution():
    """Test DNS resolution for Resend API"""
    print("🔍 Testing DNS resolution...")
    try:
        ip = socket.gethostbyname('api.resend.com')
        print(f"✅ DNS resolution successful: api.resend.com -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        return False

def test_basic_connectivity():
    """Test basic connectivity to Resend API"""
    print("\n🌐 Testing basic connectivity...")
    try:
        # Test with a simple GET request (no auth needed)
        response = requests.get('https://api.resend.com', timeout=5)
        print(f"✅ Basic connectivity successful: Status {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        print("❌ Connection timed out after 5 seconds")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_api_authentication():
    """Test API authentication"""
    print("\n🔑 Testing API authentication...")
    
    api_key = os.environ.get('RESEND_API_KEY')
    if not api_key:
        print("❌ RESEND_API_KEY not found")
        return False
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get('https://api.resend.com/domains', headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ API authentication successful")
            return True
        else:
            print(f"❌ API authentication failed: Status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except requests.exceptions.Timeout:
        print("❌ API authentication timed out after 10 seconds")
        return False
    except Exception as e:
        print(f"❌ API authentication error: {e}")
        return False

def test_email_sending_with_retries():
    """Test email sending with retry logic"""
    print("\n📧 Testing email sending with retries...")
    
    api_key = os.environ.get('RESEND_API_KEY')
    if not api_key:
        print("❌ RESEND_API_KEY not found")
        return False
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    email_data = {
        'from': '<EMAIL>',
        'to': ['<EMAIL>'],  # Use account owner's email
        'subject': '🧪 Network Connectivity Test',
        'text': 'This is a test email to verify network connectivity to Resend API.'
    }
    
    max_retries = 3
    timeout_seconds = 10
    retry_delays = [1, 2, 5]
    
    for attempt in range(max_retries):
        try:
            print(f"🔄 Attempt {attempt + 1}/{max_retries}...")
            
            start_time = time.time()
            response = requests.post(
                'https://api.resend.com/emails',
                headers=headers,
                json=email_data,
                timeout=timeout_seconds
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            print(f"⏱️  Response time: {response_time:.2f} seconds")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Email sent successfully!")
                print(f"   Email ID: {result.get('id', 'N/A')}")
                return True
            else:
                print(f"❌ Email sending failed: Status {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                wait_time = retry_delays[attempt]
                print(f"⏰ Request timed out after {timeout_seconds}s. Retrying in {wait_time}s...")
                time.sleep(wait_time)
            else:
                print(f"❌ All {max_retries} attempts timed out")
                return False
                
        except requests.exceptions.ConnectionError as e:
            if attempt < max_retries - 1:
                wait_time = retry_delays[attempt]
                print(f"🌐 Connection error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
            else:
                print(f"❌ Connection failed after {max_retries} attempts")
                return False
                
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    return False

def main():
    print("🧪 Network Connectivity Test for Resend API")
    print("=" * 60)
    
    # Load environment
    load_env()
    
    # Run tests
    dns_ok = test_dns_resolution()
    basic_ok = test_basic_connectivity()
    auth_ok = test_api_authentication()
    email_ok = test_email_sending_with_retries()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   DNS Resolution: {'✅ PASS' if dns_ok else '❌ FAIL'}")
    print(f"   Basic Connectivity: {'✅ PASS' if basic_ok else '❌ FAIL'}")
    print(f"   API Authentication: {'✅ PASS' if auth_ok else '❌ FAIL'}")
    print(f"   Email Sending: {'✅ PASS' if email_ok else '❌ FAIL'}")
    
    if all([dns_ok, basic_ok, auth_ok, email_ok]):
        print("\n🎉 All tests passed! Network connectivity is working.")
    else:
        print("\n❌ Some tests failed. Check your network connection and firewall settings.")
        print("\n💡 Troubleshooting tips:")
        print("   1. Check your internet connection")
        print("   2. Verify firewall/proxy settings allow HTTPS to api.resend.com")
        print("   3. Try using a VPN if behind corporate firewall")
        print("   4. Check if your ISP blocks certain domains")

if __name__ == '__main__':
    main()
