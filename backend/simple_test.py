import os
import requests

# Load environment variables from .env file
def load_env():
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load environment
load_env()

# Test Resend API
api_key = os.environ.get('RESEND_API_KEY')
print(f"API Key: {api_key[:10] if api_key else 'None'}...")

if api_key:
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    # Test email data
    email_data = {
        'from': '<EMAIL>',
        'to': ['<EMAIL>'],
        'subject': 'Test Email from HiSage',
        'html': '''
        <html>
        <body>
            <h2>🧠 HiSage Test Email</h2>
            <p>This is a test email to verify Resend integration.</p>
            <p>If you receive this email, the configuration is working correctly!</p>
            <hr>
            <p><small>HiSage Team</small></p>
        </body>
        </html>
        ''',
        'text': 'This is a test email to verify Resend integration.'
    }
    
    try:
        response = requests.post(
            'https://api.resend.com/emails',
            headers=headers,
            json=email_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Email sent successfully!")
        else:
            print("❌ Email sending failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
else:
    print("❌ No API key found")
