#!/usr/bin/env python
"""
Simple test for verification email sending
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from api.email_utils import send_verification_email
from api.models import CustomUser

def main():
    print("📧 Simple Verification Email Test")
    print("=" * 50)
    
    # Test email address
    test_email = '<EMAIL>'
    
    # Create or get test user
    try:
        user, created = CustomUser.objects.get_or_create(
            email=test_email,
            defaults={
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': False
            }
        )
        
        print(f"📝 Test user: {test_email}")
        
        # Send verification email
        print(f"🚀 Sending verification email...")
        
        result = send_verification_email(test_email)
        
        if result:
            print("✅ SUCCESS: Verification email sent!")
            print("💡 Check <EMAIL> for the email")
            print("💡 The verification code will <NAME_EMAIL>")
        else:
            print("❌ FAILED: Could not send verification email")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == '__main__':
    main()
