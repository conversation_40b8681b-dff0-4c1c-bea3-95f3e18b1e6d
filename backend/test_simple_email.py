#!/usr/bin/env python
"""
最简单的邮件发送测试
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings

def test_simple_email():
    """最简单的邮件发送测试"""
    print("📧 最简单的邮件发送测试")
    print("=" * 50)
    
    try:
        print(f"📝 发送方: {settings.DEFAULT_FROM_EMAIL}")
        print(f"📝 接收方: <EMAIL>")
        print(f"📝 邮件后端: {settings.EMAIL_BACKEND}")
        
        # 发送最简单的邮件
        send_mail(
            subject='测试邮件',
            message='这是一个测试邮件',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],
            fail_silently=False
        )
        
        print("✅ 邮件发送成功！")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        return False

if __name__ == '__main__':
    success = test_simple_email()
    if success:
        print("\n🎉 测试通过！邮件系统工作正常。")
    else:
        print("\n❌ 测试失败！请检查配置。")
