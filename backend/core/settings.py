"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 4.2.11.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
import os.path
from pathlib import Path
from datetime import timedelta

from dotenv import load_dotenv
BASE_DIR = Path(__file__).resolve().parent.parent
env_path = BASE_DIR / '.env'
load_dotenv(env_path)

# URL配置 - 前后端分离项目的地址配置
LOCAL_BASE_URL = "http://**************:8000"  # 前端地址
API_BASE_URL = "http://**************:8001"    # 后端API地址

# BASE_DIR is already defined above when loading .env file

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-%^wzd_ypu7j5fl%&#+u))cicp&bz&iq3i#-p$*mynq8=69jd$j"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",                # for RESTful API development
    "rest_framework.authtoken",      # DRF built-in token authentication
    "rest_framework_simplejwt",
    "api",
    "corsheaders",
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS middleware should be first
    # Temporarily disabled due to missing geoip2 dependency
    # 'middleware.AutoTimezoneMiddleware',           # Note: not backend_app.middleware.AutoTimezoneMiddleware
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

CORS_ALLOWED_ORIGINS = [
    LOCAL_BASE_URL, # Explicitly allow frontend IP
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

# For development, allow all origins (more permissive)
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

from corsheaders.defaults import default_headers
CORS_ALLOW_HEADERS = list(default_headers) + [
    'authorization',
    'content-type',
    'accept',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Allow all HTTP methods
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# Disable CSRF for API endpoints
CSRF_TRUSTED_ORIGINS = [
    LOCAL_BASE_URL,
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True
TIME_ZONE = 'America/New_York'


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

STATIC_ROOT = os.path.join(BASE_DIR, 'static')

STATICFILES_DIRS = [
    # os.path.join(BASE_DIR, 'staticfiles')
    os.path.abspath(os.path.join(BASE_DIR, '../web_app/staticfiles/static'))  # 这里要使用前端的staticfiles/static里面的文件, 现在前后和后端都放在同一个文件夹下面
]

# DRF的全局配置
REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "rest_framework.schemas.coreapi.AutoSchema",
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 50,
    "DATETIME_FORMAT": "%Y-%m-%d %H:%M:%S",
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    "DEFAULT_PARSER_CLASSES": [  # Parse request.data
        "rest_framework.parsers.JSONParser",
        "rest_framework.parsers.FormParser",
        "rest_framework.parsers.MultiPartParser",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.TokenAuthentication",
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ]
}

""" 
token 生成方法
1. Use django manage.py to generate token (for admin testing)
    python manage.py drf_create_token admin (add token for admin)
2. Generate token via django signal mechanism (generate token when creating user)
"""

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = 'api.CustomUser'
MEDIA_DIR = "media"
MEDIA_ROOT = BASE_DIR / MEDIA_DIR
MEDIA_URL = "/media/"

# celery
CELERY_BROKER_URL = "redis://localhost:6379/1"
CELERY_RESULT_BACKEND = "redis://localhost:6379/2"
CELERY_WORKER_CONCURRENCY = 1

# Email settings
EMAIL_SERVICE = os.environ.get('EMAIL_SERVICE')  # console, ses, smtp, resend
print(f'EMAIL_SERVICE from environment: {EMAIL_SERVICE}')

# Debug: Print all environment variables that start with EMAIL_
print("Environment variables starting with EMAIL_:")
for key, value in os.environ.items():
    if key.startswith('EMAIL_'):
        print(f"  {key} = {value}")

if EMAIL_SERVICE == 'resend':
    # Simple Resend API configuration - single attempt debugging
    EMAIL_BACKEND = 'api.simple_resend_backend.SimpleResendEmailBackend'

    # Security check
    if not os.environ.get('RESEND_API_KEY'):
        print("⚠️  Warning: Resend API key not configured, email sending will fail")
    else:
        print("📧 Using Simple Resend email backend for debugging")

elif EMAIL_SERVICE == 'ses':
    # Amazon SES 配置
    EMAIL_BACKEND = 'django_ses.SESBackend'
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_SES_REGION_NAME = os.environ.get('AWS_SES_REGION_NAME', 'us-east-1')
    AWS_SES_REGION_ENDPOINT = f'email.{AWS_SES_REGION_NAME}.amazonaws.com'
    AWS_SES_AUTO_THROTTLE = 0.5  # 发送速率控制

    # 安全检查
    if not all([AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY]):
        print("⚠️  警告: AWS凭证未配置，邮件发送将失败")

elif EMAIL_SERVICE == 'smtp':
    # SMTP 配置（Gmail示例）
    EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
    EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
    EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
    EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
    EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
    EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')

    if not all([EMAIL_HOST_USER, EMAIL_HOST_PASSWORD]):
        print("⚠️  警告: SMTP凭证未配置，邮件发送将失败")

else:
    # 开发环境：邮件打印到控制台
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
    print("📧 使用控制台邮件后端（开发模式）")

DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# JWt
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=120),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=14),
}

# stripe
# 导入stripe密钥
# 根据环境覆盖密钥
if os.environ.get('DJANGO_ENV') == 'test':
    STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_PUBLISHABLE_KEY')
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')
# 在生产环境中使用真实密钥
elif os.environ.get('DJANGO_ENV') == 'production':
    STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_LIVE_PUBLISHABLE_KEY')
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_LIVE_SECRET_KEY')

# print('STRIPE_PUBLISHABLE_KEY', STRIPE_PUBLISHABLE_KEY)
# print('STRIPE_SECRET_KEY', STRIPE_SECRET_KEY)