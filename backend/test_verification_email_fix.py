#!/usr/bin/env python
"""
Test verification email sending with network resilience and recipient handling
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from api.email_utils import send_verification_email, _is_domain_verified_for_email
from api.models import CustomUser

def test_domain_verification_check():
    """Test domain verification checking"""
    print("🔍 Testing domain verification check...")
    
    is_verified = _is_domain_verified_for_email()
    print(f"   Domain hisage.health verified: {'✅ YES' if is_verified else '❌ NO'}")
    
    if not is_verified:
        print("   💡 This means emails will be sent to account owner (<EMAIL>)")
        print("   💡 To send to any email, verify your domain at https://resend.com/domains")
    
    return is_verified

def test_verification_email_sending():
    """Test verification email sending with network resilience"""
    print("\n📧 Testing verification email sending...")
    
    # Test email address
    test_email = '<EMAIL>'
    
    # Create or get test user
    try:
        user, created = CustomUser.objects.get_or_create(
            email=test_email,
            defaults={
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': False
            }
        )
        
        if created:
            print(f"   📝 Created test user: {test_email}")
        else:
            print(f"   📝 Using existing test user: {test_email}")
            user.is_active = False
            user.save()
        
        # Test sending verification email
        print(f"   🚀 Attempting to send verification email to: {test_email}")
        
        result = send_verification_email(test_email)
        
        if result:
            print("   ✅ Verification email sent successfully!")
            return True
        else:
            print("   ❌ Verification email sending failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
        return False

def test_network_connectivity_quick():
    """Quick network connectivity test"""
    print("\n🌐 Quick network connectivity test...")
    
    import requests
    
    try:
        response = requests.get('https://api.resend.com', timeout=5)
        print(f"   ✅ Network connectivity: OK (Status: {response.status_code})")
        return True
    except requests.exceptions.Timeout:
        print("   ❌ Network connectivity: TIMEOUT")
        return False
    except Exception as e:
        print(f"   ❌ Network connectivity: ERROR ({e})")
        return False

def main():
    print("🧪 Testing Verification Email Fix")
    print("=" * 60)
    
    # Test network connectivity first
    network_ok = test_network_connectivity_quick()
    
    if not network_ok:
        print("\n❌ Network connectivity issues detected.")
        print("💡 Please check your internet connection and try again.")
        return
    
    # Test domain verification
    domain_verified = test_domain_verification_check()
    
    # Test verification email sending
    email_ok = test_verification_email_sending()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Network Connectivity: {'✅ PASS' if network_ok else '❌ FAIL'}")
    print(f"   Domain Verification: {'✅ VERIFIED' if domain_verified else '⚠️  NOT VERIFIED'}")
    print(f"   Verification Email: {'✅ PASS' if email_ok else '❌ FAIL'}")
    
    if email_ok:
        print("\n🎉 Verification email system is working correctly!")
        if not domain_verified:
            print("\n💡 Note: Emails are being sent to account owner due to unverified domain.")
            print("   The verification codes will still work for the intended users.")
            print("   To send to any email address, verify your domain at:")
            print("   https://resend.com/domains")
    else:
        print("\n❌ Verification email system needs attention.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Check network connectivity")
        print("   2. Verify Resend API key is correct")
        print("   3. Check firewall/proxy settings")
        print("   4. Try running: python test_network_connectivity.py")

if __name__ == '__main__':
    main()
