#!/usr/bin/env python
"""
Test script to verify Django email configuration with <PERSON>send
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from api.email_utils import send_verification_email

def test_basic_email():
    """Test basic email sending using Django's send_mail"""
    try:
        print("=" * 60)
        print("🧪 Testing basic email sending...")
        print("=" * 60)
        
        print(f"📧 EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
        print(f"📧 DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
        print(f"📧 EMAIL_SERVICE: {os.environ.get('EMAIL_SERVICE')}")
        
        result = send_mail(
            subject='🧠 HiSage Test Email',
            message='This is a test email to verify Resend integration.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=['<EMAIL>'],  # Use account owner's email for testing
            html_message='''
            <html>
            <body>
                <h2>🧠 HiSage Test Email</h2>
                <p>This is a test email to verify Resend integration.</p>
                <p>If you receive this email, the configuration is working correctly!</p>
                <hr>
                <p><small>HiSage Team</small></p>
            </body>
            </html>
            ''',
            fail_silently=False
        )
        
        if result:
            print("✅ Basic email sent successfully!")
            return True
        else:
            print("❌ Basic email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Error sending basic email: {e}")
        return False

def test_verification_email():
    """Test verification email sending"""
    try:
        print("\n" + "=" * 60)
        print("🧪 Testing verification email...")
        print("=" * 60)
        
        # Create a test user first
        from api.models import CustomUser
        
        test_email = '<EMAIL>'  # Use account owner's email for testing
        
        # Check if user exists, create if not
        user, created = CustomUser.objects.get_or_create(
            email=test_email,
            defaults={
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': False  # Not activated yet
            }
        )
        
        if created:
            print(f"📝 Created test user: {test_email}")
        else:
            print(f"📝 Using existing test user: {test_email}")
            # Make sure user is not activated for testing
            user.is_active = False
            user.save()
        
        # Send verification email
        result = send_verification_email(test_email)
        
        if result:
            print("✅ Verification email sent successfully!")
            return True
        else:
            print("❌ Verification email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Error sending verification email: {e}")
        return False

def main():
    print("🧪 Testing Django Email Configuration with Resend")
    print("=" * 60)
    
    # Test basic email
    basic_success = test_basic_email()
    
    # Test verification email
    verification_success = test_verification_email()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Basic Email: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"   Verification Email: {'✅ PASS' if verification_success else '❌ FAIL'}")
    
    if basic_success and verification_success:
        print("\n🎉 All tests passed! Email configuration is working correctly.")
        print("\n💡 Note: For production use, verify your domain at https://resend.com/domains")
        print("   and update DEFAULT_FROM_EMAIL to use your verified domain.")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")

if __name__ == '__main__':
    main()
