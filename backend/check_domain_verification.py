#!/usr/bin/env python
"""
Check domain verification status in Resend
"""
import os
import requests

# Load environment variables from .env file
def load_env():
    env_path = '.env'
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

# Load environment
load_env()

def check_domain_verification():
    """Check domain verification status"""
    api_key = os.environ.get('RESEND_API_KEY')
    
    if not api_key:
        print("❌ RESEND_API_KEY not found in environment variables")
        return
    
    print(f"🔑 Using API Key: {api_key[:10]}...")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("\n" + "="*60)
        print("🔍 Checking Domain Verification Status")
        print("="*60)
        
        response = requests.get('https://api.resend.com/domains', headers=headers, timeout=10)
        
        if response.status_code == 200:
            domains = response.json()
            
            print(f"📊 Total domains: {len(domains.get('data', []))}")
            
            for domain_info in domains.get('data', []):
                domain_name = domain_info.get('name', 'Unknown')
                status = domain_info.get('status', 'Unknown')
                created_at = domain_info.get('created_at', 'Unknown')
                
                status_emoji = "✅" if status == "verified" else "⚠️" if status == "pending" else "❌"
                
                print(f"\n📧 Domain: {domain_name}")
                print(f"   Status: {status_emoji} {status.upper()}")
                print(f"   Created: {created_at}")
                
                if domain_name == 'hisage.health':
                    if status == 'verified':
                        print(f"   🎉 Your domain is verified! You can <NAME_EMAIL>")
                        print(f"   💡 Update your .env file: DEFAULT_FROM_EMAIL=<EMAIL>")
                    else:
                        print(f"   ⚠️  Domain not verified yet. Current status: {status}")
                        print(f"   📝 Please complete DNS verification in Resend dashboard")
                        print(f"   🔗 Visit: https://resend.com/domains")
            
            # Check if hisage.health domain exists
            domain_names = [d.get('name') for d in domains.get('data', [])]
            if 'hisage.health' not in domain_names:
                print(f"\n❌ Domain 'hisage.health' not found in your Resend account")
                print(f"   📝 Please add it at: https://resend.com/domains")
                print(f"   ➕ Click 'Add Domain' and enter: hisage.health")
        
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error checking domains: {e}")

def main():
    print("🔍 Resend Domain Verification Checker")
    print("="*60)
    
    check_domain_verification()
    
    print(f"\n" + "="*60)
    print("📋 Next Steps:")
    print("1. If domain not added: Go to https://resend.com/domains and add 'hisage.health'")
    print("2. If domain pending: Complete DNS verification steps")
    print("3. If domain verified: Update DEFAULT_FROM_EMAIL in .env file")
    print("4. Test with: python test_django_email.py")

if __name__ == '__main__':
    main()
